<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
      <meta charset="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
      <!-- Favicon icon-->
      <link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
      <link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
      <link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
      <link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
      <meta name="msapplication-TileColor" content="#8b3dff" />
      <meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />
      <!-- Color modes -->
      <script src="./assets/js/vendors/color-modes.js"></script>
      <!-- Libs CSS -->
      <link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
      <link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
      <!-- Scroll Cue -->
      <link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />
      <!-- Box icons -->
      <link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />
      <!-- Theme CSS -->
      <link rel="stylesheet" href="./assets/css/theme.min.css">
      <title>Become An Aidapay Agent | Build a Lifetime Income</title>
   </head>
   <body>
      <!-- Navbar -->
      <header>
         <div class="container">
            <nav class="navbar navbar-expand-lg navbar-boxed mx-auto mt-lg-3">
               <a class="navbar-brand" href="./index.html"><img src="https://www.aidapay.ng/images/logo.svg" style="width: 80%;" alt="Aidapay Logo" /></a>
               <button
                  class="navbar-toggler"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#navbarSupportedContent"
                  aria-controls="navbarSupportedContent"
                  aria-expanded="false"
                  aria-label="Toggle navigation">
                  <i class="bi bi-list"></i>
               </button>
               <div class="collapse navbar-collapse" id="navbarSupportedContent">
                  <ul class="navbar-nav mx-auto align-items-lg-center">
                     <li class="nav-item">
                        <a class="nav-link" href="./index.html">Home</a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" href="./service.html">Services</a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" href="./contact.html">Contact</a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link active" href="./become-an-agent.html">Become An Agent</a>
                     </li>
                  </ul>
                  <div class="mt-3 mt-lg-0 d-flex align-items-center">
                     <a href="./signin.html" class="btn btn-light mx-2">Login</a>
                     <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
                  </div>
               </div>
            </nav>
         </div>
      </header>
      <main>
         <!--Hero start-->
         <div class="pattern-square"></div>
         <section class="py-xl-8 py-lg-7 py-5 position-relative z-1" data-cue="fadeIn">
            <div class="container pb-xl-8 mb-xl-8">
               <div class="row align-items-center gy-6">
                  <div class="col-lg-5 col-xl-5" data-cue="zoomOut">
                     <div class="d-flex flex-column gap-4">
                        <div class="d-flex flex-row gap-2 align-items-center lh-1 mt-10">
                           <span>
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-person-plus-fill text-primary" viewBox="0 0 16 16">
                                 <path d="M1 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H1zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                                 <path fill-rule="evenodd" d="M13.5 5a.5.5 0 0 1 .5.5V7h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V8h-1.5a.5.5 0 0 1 0-1H13V5.5a.5.5 0 0 1 .5-.5z"/>
                              </svg>
                           </span>
                           <h1 class="fs-5 mb-0 ">The Aidapay Agent Program</h1>
                        </div>
                        <div>
                           <h2 class="display-6 mb-3">
                              Stop Earning a Bonus.
                              <span class="text-primary">Start Building a Business.</span>
                           </h2>
                           <p class="lead">
                              Don't settle for small, one-time rewards. Partner with Aidapay to build a real, recurring monthly income stream. Every time your referrals pay a bill, buy data, or use ScanPay, you get paid. For life.
                           </p>
                           <ul class="list-unstyled d-flex flex-column gap-2">
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Lifetime Recurring Commissions</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Commissions on Total Transaction Value</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Instant Access to Your Earnings</span>
                              </li>
                           </ul>
                        </div>
                        <div class="d-flex flex-row gap-3 align-items-center">
                                            <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1"><img src="../assets/images/mobile-app/playstore.svg" alt="Get it on Google Play" /></a>
                 <a href="https://apps.apple.com/ua/app/aidapay-cheap-data-airtime/id6448542163"><img src="../assets/images/mobile-app/appstore.svg" alt="Download on the App Store" /></a>
                           
                        
                        </div>
                     </div>
                  </div>
                  <div class="offset-xl-1 col-xl-6 col-lg-6" data-cue="zoomIn">
                     <div class="position-relative ms-lg-8 ms-xl-0 me-xl-7">
                        <img src="../assets/images/seo/hero-img.jpg" alt="Agent proudly showing Aidapay app" class="w-100 rounded-3" width="480" />
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <div class="position-relative d-none d-xl-block">
            <div class="position-absolute bottom-0 w-100 z-n1">
               <img src="./assets/images/seo/gradient.svg" alt="" class="w-100" />
            </div>
            <div class="d-flex position-absolute bottom-0 start-0 w-100 overflow-hidden mb-n1" style="color: var(--bs-body-bg)">
               <div class="position-relative start-50 translate-middle-x flex-shrink-0" style="width: 3774px">
                  <svg width="3774" height="99" viewBox="0 0 3774 99" fill="none" xmlns="http://www.w3.org/2000/svg">
                     <path d="M0 200.003C0 200.003 1137.52 0.188224 1873.5 0.000134392C2614.84 -0.189325 3774 200.003 3774 200.003H0Z" fill="currentColor"></path>
                  </svg>
               </div>
            </div>
         </div>
         <!--Hero end-->
         <!-- Your Earning Streams Section -->
         <section class="my-xl-7 py-5" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                     <div class="text-center mb-6 mb-lg-9">
                        <small class="text-primary text-uppercase ls-md fw-semibold">Your Revenue Streams</small>
                        <h2 class="my-3">Four Powerful Ways to Earn with Every Referral</h2>
                        <p class="mb-0 lead">Your earnings are diversified. You earn a significant commission on the total amount of almost every transaction your referrals make.</p>
                     </div>
                  </div>
               </div>
               <div class="row g-lg-7 gy-5">
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-qr-code-scan text-primary fs-3"></i>
                        </div>
                        <h4>Scan & Pay Transactions</h4>
                        <p class="mb-0">You earn a massive <strong class="text-primary"> ₦5 flat commission </strong> every time your referral uses our revolutionary ScanPay feature. This is your high-frequency earner.</p>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-wifi text-primary fs-3"></i>
                        </div>
                        <h4>Data Bundle Purchases</h4>
                        <p class="mb-0">Earn an incredible <strong class="text-primary">10% commission on the total amount</strong> of every data bundle they buy. If they buy a ₦1,000 bundle, **you instantly earn ₦100.**</p>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-phone-fill text-primary fs-3"></i>
                        </div>
                        <h4>Airtime Top-ups</h4>
                        <p class="mb-0">Earn a solid <strong class="text-primary">3% commission on the total amount</strong>  of all airtime top-ups. These small, daily transactions quickly build into a significant income.</p>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-lightbulb-fill text-primary fs-3"></i>
                        </div>
                        <h4>Electricity Bill Payments</h4>
                        <p class="mb-0">Earn a <strong class="text-primary">5% commission on the total amount</strong>  paid for electricity tokens. Help your community stay powered on and get rewarded for it.</p>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!-- NEW Section: The Earning Potential -->
         <section class="my-xl-7 py-5 bg-light-subtle" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-lg-10 offset-lg-1 col-md-12 col-12">
                     <div class="text-center mb-6 mb-lg-9">
                        <small class="text-primary text-uppercase ls-md fw-semibold">Calculate Your Growth</small>
                        <h2 class="my-3">See Your Scan & Pay Earning Potential</h2>
                        <p class="mb-0 lead">
                           Use the sliders below to see how your monthly income from Scan & Pay grows based on the number of users you refer and how many transactions they make.
                        </p>
                        <div class="d-flex justify-content-center align-items-center my-4">
                           <div class="w-75">
                              <label for="userSlider" class="form-label">Number of Users</label>
                              <input type="range" class="form-range" id="userSlider" min="1" max="100" value="10" />
                              <div class="d-flex justify-content-between">
                                 <span>1 User</span>
                                 <span class="text-primary fw-bold" id="userCount">10 Users</span>
                                 <span>100 Users</span>
                              </div>
                           </div>
                        </div>
                        <div class="d-flex justify-content-center align-items-center my-4">
                           <div class="w-75">
                              <label for="transactionSlider" class="form-label">Transactions per User (Monthly)</label>
                              <input type="range" class="form-range" id="transactionSlider" min="1" max="60" value="10" />
                              <div class="d-flex justify-content-between">
                                 <span>1 Transaction</span>
                                 <span class="text-primary fw-bold" id="transactionCount">10 Transactions</span>
                                 <span>60 Transactions</span>
                              </div>
                           </div>
                        </div>
                        <small class="text-body-tertiary">(*Calculations are based on a ₦5 commission per Scan & Pay transaction.)</small>
                     </div>
                     <div class="table-responsive">
                        <table class="table table-striped table-hover text-center">
                           <thead class="table-light">
                              <tr>
                                 <th>Month</th>
                                 <th>Total Active Users</th>
                                 <th>Total Monthly Transactions</th>
                                 <th>Total Monthly Income</th>
                              </tr>
                           </thead>
                           <tbody id="earningsTableBody">
                              <tr>
                                 <td>Month 1</td>
                                 <td data-month="1">10</td>
                                 <td>100</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦500</span></td>
                              </tr>
                              <tr>
                                 <td>Month 2</td>
                                 <td data-month="2">20</td>
                                 <td>200</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦1,000</span></td>
                              </tr>
                              <tr>
                                 <td>Month 3</td>
                                 <td data-month="3">30</td>
                                 <td>300</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦1,500</span></td>
                              </tr>
                              <tr>
                                 <td>Month 6</td>
                                 <td data-month="6">60</td>
                                 <td>600</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦3,000</span></td>
                              </tr>
                              <tr>
                                 <td>Month 12</td>
                                 <td data-month="12">120</td>
                                 <td>1200</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦6,000</span> per month</td>
                              </tr>
                           </tbody>
                        </table>
                     </div>
                     <p class="text-center mt-3 text-body-tertiary">Your income grows every month as you refer more users. This is the power of a lifetime recurring revenue model.</p>
                  </div>
               </div>
            </div>
         </section>
         <!-- How It Works Section -->
         <section class="my-xl-7 py-5">
            <div class="container">
               <div class="row align-items-center">
                  <div class="col-lg-5 col-md-6 col-12" data-cue="slideInLeft">
                     <div data-cue="fadeIn">
                        <small class="text-primary text-uppercase ls-md fw-semibold">Simple & Transparent</small>
                        <div class="mb-5 mt-4">
                           <h2 class="mb-3">Your Dashboard, Your Money, Your Control.</h2>
                           <p class="mb-0 lead">We believe you should have full control over the money you earn. Our system is built for clarity and immediate access.</p>
                        </div>
                        <ul class="list-unstyled mb-5">
                           <li class="mb-3 d-flex">
                              <i class="bi bi-eye-fill text-primary mt-1 fs-4"></i>
                              <span class="ms-3">
                                 <strong>Track Your Earnings in Real-Time:</strong> See your commissions the moment they come in.
                              </span>
                           </li>
                           <li class="mb-3 d-flex">
                              <i class="bi bi-clock-history text-primary mt-1 fs-4"></i>
                              <span class="ms-3">
                                 <strong>Access Funds Instantly:</strong> Your money is available immediately. No more waiting for payout periods.
                              </span>
                           </li>
                           <li class="mb-3 d-flex">
                              <i class="bi bi-bank2 text-primary mt-1 fs-4"></i>
                              <span class="ms-3">
                                 <strong>Use or Withdraw Anytime:</strong> Transfer your earnings to your bank or use them to pay your own bills directly from the app.
                              </span>
                           </li>
                        </ul> 
                     </div>
                  </div>
                  <div class="col-lg-6 offset-lg-1 col-md-6 col-12" data-cue="slideInRight">
                     <div class="position-relative">
                        <figure>
                           <img src="./assets/images/landings/saas/saas-img-1.jpg" alt="Agent dashboard on phone" class="img-fluid rounded-4" />
                        </figure>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action start-->
         <section class="py-5" id="apply">
            <div class="container" data-cues="zoomIn">
               <div class="row bg-pattern bg-primary-gradient rounded-3 p-7 g-0">
                  <div class="col-xl-8 offset-xl-2 col-lg-10 offset-lg-1 col-md-12 col-12">
                     <div class="position-relative z-1 my-lg-5">
                        <div class="mb-5 text-center">
                           <h3 class="h2 text-white-stable mb-1">Ready to Build Your Business with Aidapay?</h3>
                           <p class="mb-0 text-white-stable">Apply to become an agent today. It's free, takes less than 5 minutes, and is the first step towards building your lifetime income stream.</p>
                        </div>
                        <form class="row g-2 needs-validation d-flex mx-lg-7" novalidate>
                           <div class="col-md-7 col-xl-7 col-12">
                              <label for="notificationEmail" class="visually-hidden"></label>
                              <input type="email" id="notificationEmail" class="form-control" placeholder="Enter your email to get started" aria-label="Enter your email to get started" required />
                              <div class="invalid-feedback text-start">Please enter a valid email.</div>
                           </div>
                           <div class="col-md-5 col-xl-5 col-12">
                              <div class="d-grid">
                                 <button class="btn btn-dark" type="submit">Apply Now for Free</button>
                              </div>
                           </div>
                        </form>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action end-->
      </main>
      <footer class="pt-7">
         <div class="container">
            <!-- Footer 3 column -->
            <div class="row">
               <div class="col-lg-9 col-12">
                  <div class="row" id="ft-links">
                     <div class="col-lg-4 col-md-4 col-12"> <!-- Adjusted grid for better spacing with 3 columns -->
                        <div class="position-relative">
                           <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                              <h4>Services</h4>
                              <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseServices" role="button" aria-expanded="true" aria-controls="collapseServices">
                                 <i class="bi bi-chevron-down"></i>
                              </a>
                           </div>
                           <div class="d-lg-block collapse show" id="collapseServices" data-bs-parent="#ft-links" style="">
                              <ul class="list-unstyled mb-0 py-3 py-lg-0">
                                 <li class="mb-2">
                                    <a href="./scan-pay.html" class="text-decoration-none text-reset">Scan & Pay</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="./service.html" class="text-decoration-none text-reset">Data Bundle</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="./service.html" class="text-decoration-none text-reset">Airtime Topup</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="./service.html" class="text-decoration-none text-reset">Bill Payments</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="./service.html" class="text-decoration-none text-reset">Funds Transfer</a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-4 col-md-4 col-12"> <!-- Adjusted grid for better spacing with 3 columns -->
                        <div>
                           <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                              <h4>Company</h4>
                              <a
                                 class="d-block d-lg-none stretched-link text-body collapsed"
                                 data-bs-toggle="collapse"
                                 href="#collapseCompany"
                                 role="button"
                                 aria-expanded="false"
                                 aria-controls="collapseCompany">
                                 <i class="bi bi-chevron-down"></i>
                              </a>
                           </div>
                           <div class="collapse d-lg-block" id="collapseCompany" data-bs-parent="#ft-links">
                              <ul class="list-unstyled mb-0 py-3 py-lg-0">
                                 <li class="mb-2">
                                    <a href="./about.html" class="text-decoration-none text-reset">About Us</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="./become-an-agent.html" class="text-decoration-none text-reset">Become An Agent</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="#!" class="text-decoration-none text-reset">Developers API</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="./contact.html" class="text-decoration-none text-reset">Contact Us</a>
                                 </li>
                                 <li class="mb-2">
                                    <a href="./signin.html" class="text-decoration-none text-reset">Login / Register</a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-4 col-md-4 col-12"> <!-- Adjusted grid for better spacing with 3 columns -->
                        <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                           <h4>Support</h4>
                           <a
                              class="d-block d-lg-none stretched-link text-body collapsed"
                              data-bs-toggle="collapse"
                              href="#collapseSupport"
                              role="button"
                              aria-expanded="false"
                              aria-controls="collapseSupport">
                              <i class="bi bi-chevron-down"></i>
                           </a>
                        </div>
                        <div class="collapse d-lg-block" id="collapseSupport" data-bs-parent="#ft-links">
                           <ul class="list-unstyled mb-0 py-3 py-lg-0">
                              <li class="mb-2">
                                 <a href="./privacy-policy.html" class="text-decoration-none text-reset">Privacy Policy</a>
                              </li>
                              <li class="mb-2">
                                 <a href="./terms-of-service.html" class="text-decoration-none text-reset">Terms & Conditions</a>
                              </li>
                              <li class="mb-2">
                                 <a href="./faq.html" class="text-decoration-none text-reset">FAQs</a>
                              </li>
                           </ul>
                        </div>
                     </div>
                     <!-- NEWSLETTER COLUMN REMOVED -->
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="me-7">
                     <h4 class="mb-4">Contact Us</h4>
                     <p class="text-body-secondary">For any inquiries or support, please email us. We are available 24/7 to assist you.</p>
                     <a href="mailto:<EMAIL>" class="text-primary fw-semibold"><EMAIL></a>
                                                     <div class="d-flex gap-2 justify-content-left mt-2">
                 <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1"><img src="../assets/images/mobile-app/playstore.svg" alt="Get it on Google Play" /></a>
                 <a href="https://apps.apple.com/ua/app/aidapay-cheap-data-airtime/id6448542163"><img src="../assets/images/mobile-app/appstore.svg" alt="Download on the App Store" /></a>
              </div>
                  </div>
               </div>
            </div>
         </div>
         <div class="container mt-7 pt-lg-7 pb-4">
            <div class="row align-items-center">
               <div class="col-md-3">
                  <a class="mb-4 mb-lg-0 d-block text-inverse" href="#"><img src="./assets/images/logo/logo_aidapay.svg" alt="Aidapay Logo" /></a>
               </div>
               <div class="col-md-9 col-lg-6">
                  <div class="small mb-3 mb-lg-0 text-lg-center">
                     Copyright © 2025
                     <span class="text-primary"><a href="#">Aidapay</a></span>
                     . All rights reserved.
                  </div>
               </div>
               <div class="col-lg-3">
                  <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
                     <div class="dropdown">
                        <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                           <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                           <span class="visually-hidden bs-theme-text">Toggle theme</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                           <li>
                              <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                                 <i class="bi theme-icon bi-sun-fill"></i>
                                 <span class="ms-2">Light</span>
                              </button>
                           </li>
                           <li>
                              <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                                 <i class="bi theme-icon bi-moon-stars-fill"></i>
                                 <span class="ms-2">Dark</span>
                              </button>
                           </li>
                           <li>
                              <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                                 <i class="bi theme-icon bi-circle-half"></i>
                                 <span class="ms-2">Auto</span>
                              </button>
                           </li>
                        </ul>
                     </div>
                     <div class="ms-3 d-flex gap-2">
                        <a href="https://www.tiktok.com/@aidapayng" class="btn btn-instagram btn-light btn-icon">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                              <path
                                 d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                           </svg>
                        </a>
                        <a href="https://www.facebook.com/aidapay" class="btn btn-facebook btn-icon">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                              <path
                                 d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                           </svg>
                        </a>
                        <a href="https://x.com/aidapay?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor" class="btn btn-twitter btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter-x" viewBox="0 0 16 16">
                        <path d="M12.6.75h2.454l-5.36 6.142L16 15.25h-4.937l-3.867-5.07-4.425 5.07H.316l5.733-6.57L0 .75h5.063l3.495 4.633L12.601.75Zm-.86 13.028h1.36L4.323 2.145H2.865z"/>
                        </svg>
                        </a>
                           <a href="https://www.tiktok.com/@aidapayng" class="btn btn-twitter btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-tiktok" viewBox="0 0 16 16">
                        <path d="M9 0h1.98c.144.715.54 1.617 1.235 2.512C12.895 3.389 13.797 4 15 4v2c-1.753 0-3.07-.814-4-1.829V11a5 5 0 1 1-5-5v2a3 3 0 1 0 3 3z"/>
                        </svg>
                        </a>
                        <a href="https://www.tiktok.com/@aidapayng" class="btn btn-twitter btn-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-whatsapp" viewBox="0 0 16 16">
                        <path d="M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 ***********-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232"/>
                        </svg>
                        </a>


                        
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </footer>
      <script src="../assets/js/earnings-calculator.js"></script>
       <!-- Libs JS -->
      <script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
      <script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
      <script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>
      <!-- Theme JS -->
      <script src="./assets/js/theme.min.js"></script>
      <script src="./assets/libs/parallax-js/dist/parallax.min.js"></script>
      <script src="./assets/js/vendors/parallax.js"></script>
      <script src="./assets/libs/rellax/rellax.min.js"></script>
      <script src="./assets/js/vendors/rellax.js"></script>
      <!-- Swiper JS -->
      <script src="./assets/libs/swiper/swiper-bundle.min.js"></script>
      <script src="./assets/js/vendors/swiper.js"></script>
      <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="./assets/js/vendors/scrollcue.js"></script>
      <script>
         // JavaScript for updating the earnings table based on slider input
         const userSlider = document.getElementById('userSlider');
         const userCountDisplay = document.getElementById('userCount');
         const transactionSlider = document.getElementById('transactionSlider');
         const transactionCountDisplay = document.getElementById('transactionCount');
         const earningsTableBody = document.getElementById('earningsTableBody');
         
         // Update the displayed user count and table values
         function updateEarnings() {
             const newUsersPerMonth = parseInt(userSlider.value);
             const transactionsPerUser = parseInt(transactionSlider.value);
         
             userCountDisplay.innerText = `${newUsersPerMonth} Users`;
             transactionCountDisplay.innerText = `${transactionsPerUser} Transactions`;
         
             // Calculate and update each row in the table
             for (let i = 0; i < earningsTableBody.rows.length; i++) {
                 const row = earningsTableBody.rows[i];
                 const month = parseInt(row.cells[1].getAttribute('data-month'));
                 
                 const totalUsers = newUsersPerMonth * month;
                 const totalTransactions = totalUsers * transactionsPerUser;
                 const totalIncome = totalTransactions * 5; // ₦5 commission per transaction
         
                 row.cells[1].innerText = totalUsers.toLocaleString();
                 row.cells[2].innerText = totalTransactions.toLocaleString();
                 row.cells[3].innerHTML = `<span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦${totalIncome.toLocaleString()}</span>`;
             }
         }
         
         // Initial calculation
         updateEarnings();
         
         // Event listeners for slider input
         userSlider.addEventListener('input', updateEarnings);
         transactionSlider.addEventListener('input', updateEarnings);
      </script>
   </body>
</html>