<!doctype html>
<html lang="en">
   <head>
      <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
      @@include('./partials/head/meta.html')
      @@include('./partials/head/head-links.html', {
         "webRoot": "."
      })
      <title>Become An Aidapay Agent | Build a Lifetime Income</title>
   </head>
   <body>
      @@include('./partials/navbar-boxed.html', {
         "webRoot": "."
      })
      <main>
         <!--Hero start-->
         <div class="pattern-square"></div>
         <section class="py-xl-8 py-lg-7 py-5 position-relative z-1" data-cue="fadeIn">
            <div class="container pb-xl-8 mb-xl-8">
               <div class="row align-items-center gy-6">
                  <div class="col-lg-5 col-xl-5" data-cue="zoomOut">
                     <div class="d-flex flex-column gap-4">
                        <div class="d-flex flex-row gap-2 align-items-center lh-1 mt-10">
                           <span>
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-person-plus-fill text-primary" viewBox="0 0 16 16">
                                 <path d="M1 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H1zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                                 <path fill-rule="evenodd" d="M13.5 5a.5.5 0 0 1 .5.5V7h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V8h-1.5a.5.5 0 0 1 0-1H13V5.5a.5.5 0 0 1 .5-.5z"/>
                              </svg>
                           </span>
                           <h1 class="fs-5 mb-0 ">The Aidapay Agent Program</h1>
                        </div>
                        <div>
                           <h2 class="display-6 mb-3">
                              Stop Earning a Bonus.
                              <span class="text-primary">Start Building a Business.</span>
                           </h2>
                           <p class="lead">
                              Don't settle for small, one-time rewards. Partner with Aidapay to build a real, recurring monthly income stream. Every time your referrals pay a bill, buy data, or use ScanPay, you get paid. For life.
                           </p>
                           <ul class="list-unstyled d-flex flex-column gap-2">
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Lifetime Recurring Commissions</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Commissions on Total Transaction Value</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Instant Access to Your Earnings</span>
                              </li>
                           </ul>
                        </div>
                        <div class="d-flex flex-row gap-3 align-items-center">
                                            <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1"><img src="./assets/images/mobile-app/playstore.svg" alt="Get it on Google Play" /></a>
                 <a href="https://apps.apple.com/ua/app/aidapay-cheap-data-airtime/id6448542163"><img src="./assets/images/mobile-app/appstore.svg" alt="Download on the App Store" /></a>
                           
                        
                        </div>
                     </div>
                  </div>
                  <div class="offset-xl-1 col-xl-6 col-lg-6" data-cue="zoomIn">
                     <div class="position-relative ms-lg-8 ms-xl-0 me-xl-7">
                        <img src="./assets/images/seo/hero-img.jpg" alt="Agent proudly showing Aidapay app" class="w-100 rounded-3" width="480" />
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <div class="position-relative d-none d-xl-block">
            <div class="position-absolute bottom-0 w-100 z-n1">
               <img src="./assets/images/seo/gradient.svg" alt="" class="w-100" />
            </div>
            <div class="d-flex position-absolute bottom-0 start-0 w-100 overflow-hidden mb-n1" style="color: var(--bs-body-bg)">
               <div class="position-relative start-50 translate-middle-x flex-shrink-0" style="width: 3774px">
                  <svg width="3774" height="99" viewBox="0 0 3774 99" fill="none" xmlns="http://www.w3.org/2000/svg">
                     <path d="M0 200.003C0 200.003 1137.52 0.188224 1873.5 0.000134392C2614.84 -0.189325 3774 200.003 3774 200.003H0Z" fill="currentColor"></path>
                  </svg>
               </div>
            </div>
         </div>
         <!--Hero end-->
         <!-- Your Earning Streams Section -->
         <section class="my-xl-7 py-5" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                     <div class="text-center mb-6 mb-lg-9">
                        <small class="text-primary text-uppercase ls-md fw-semibold">Your Revenue Streams</small>
                        <h2 class="my-3">Four Powerful Ways to Earn with Every Referral</h2>
                        <p class="mb-0 lead">Your earnings are diversified. You earn a significant commission on the total amount of almost every transaction your referrals make.</p>
                     </div>
                  </div>
               </div>
               <div class="row g-lg-7 gy-5">
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-qr-code-scan text-primary fs-3"></i>
                        </div>
                        <h4>Scan & Pay Transactions</h4>
                        <p class="mb-0">You earn a massive <strong class="text-primary"> ₦5 flat commission </strong> every time your referral uses our revolutionary ScanPay feature. This is your high-frequency earner.</p>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-wifi text-primary fs-3"></i>
                        </div>
                        <h4>Data Bundle Purchases</h4>
                        <p class="mb-0">Earn an incredible <strong class="text-primary">10% commission on the total amount</strong> of every data bundle they buy. If they buy a ₦1,000 bundle, **you instantly earn ₦100.**</p>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-phone-fill text-primary fs-3"></i>
                        </div>
                        <h4>Airtime Top-ups</h4>
                        <p class="mb-0">Earn a solid <strong class="text-primary">3% commission on the total amount</strong>  of all airtime top-ups. These small, daily transactions quickly build into a significant income.</p>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-6">
                     <div class="text-center text-md-start" data-cue="zoomIn">
                        <div class="icon-lg icon-shape rounded bg-primary bg-opacity-10 border border-primary-subtle mb-5">
                           <i class="bi bi-lightbulb-fill text-primary fs-3"></i>
                        </div>
                        <h4>Electricity Bill Payments</h4>
                        <p class="mb-0">Earn a <strong class="text-primary">5% commission on the total amount</strong>  paid for electricity tokens. Help your community stay powered on and get rewarded for it.</p>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!-- NEW Section: The Earning Potential -->
         <section class="my-xl-7 py-5 bg-light-subtle" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-lg-10 offset-lg-1 col-md-12 col-12">
                     <div class="text-center mb-6 mb-lg-9">
                        <small class="text-primary text-uppercase ls-md fw-semibold">Calculate Your Growth</small>
                        <h2 class="my-3">See Your Scan & Pay Earning Potential</h2>
                        <p class="mb-0 lead">
                           Use the sliders below to see how your monthly income from Scan & Pay grows based on the number of users you refer and how many transactions they make.
                        </p>
                        <div class="d-flex justify-content-center align-items-center my-4">
                           <div class="w-75">
                              <label for="userSlider" class="form-label">Number of Users</label>
                              <input type="range" class="form-range" id="userSlider" min="1" max="100" value="10" />
                              <div class="d-flex justify-content-between">
                                 <span>1 User</span>
                                 <span class="text-primary fw-bold" id="userCount">10 Users</span>
                                 <span>100 Users</span>
                              </div>
                           </div>
                        </div>
                        <div class="d-flex justify-content-center align-items-center my-4">
                           <div class="w-75">
                              <label for="transactionSlider" class="form-label">Transactions per User (Monthly)</label>
                              <input type="range" class="form-range" id="transactionSlider" min="1" max="60" value="10" />
                              <div class="d-flex justify-content-between">
                                 <span>1 Transaction</span>
                                 <span class="text-primary fw-bold" id="transactionCount">10 Transactions</span>
                                 <span>60 Transactions</span>
                              </div>
                           </div>
                        </div>
                        <small class="text-body-tertiary">(*Calculations are based on a ₦5 commission per Scan & Pay transaction.)</small>
                     </div>
                     <div class="table-responsive">
                        <table class="table table-striped table-hover text-center">
                           <thead class="table-light">
                              <tr>
                                 <th>Month</th>
                                 <th>Total Active Users</th>
                                 <th>Total Monthly Transactions</th>
                                 <th>Total Monthly Income</th>
                              </tr>
                           </thead>
                           <tbody id="earningsTableBody">
                              <tr>
                                 <td>Month 1</td>
                                 <td data-month="1">10</td>
                                 <td>100</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦500</span></td>
                              </tr>
                              <tr>
                                 <td>Month 2</td>
                                 <td data-month="2">20</td>
                                 <td>200</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦1,000</span></td>
                              </tr>
                              <tr>
                                 <td>Month 3</td>
                                 <td data-month="3">30</td>
                                 <td>300</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦1,500</span></td>
                              </tr>
                              <tr>
                                 <td>Month 6</td>
                                 <td data-month="6">60</td>
                                 <td>600</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦3,000</span></td>
                              </tr>
                              <tr>
                                 <td>Month 12</td>
                                 <td data-month="12">120</td>
                                 <td>1200</td>
                                 <td><span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦6,000</span> per month</td>
                              </tr>
                           </tbody>
                        </table>
                     </div>
                     <p class="text-center mt-3 text-body-tertiary">Your income grows every month as you refer more users. This is the power of a lifetime recurring revenue model.</p>
                  </div>
               </div>
            </div>
         </section>
         <!-- How It Works Section -->
         <section class="my-xl-7 py-5">
            <div class="container">
               <div class="row align-items-center">
                  <div class="col-lg-5 col-md-6 col-12" data-cue="slideInLeft">
                     <div data-cue="fadeIn">
                        <small class="text-primary text-uppercase ls-md fw-semibold">Simple & Transparent</small>
                        <div class="mb-5 mt-4">
                           <h2 class="mb-3">Your Dashboard, Your Money, Your Control.</h2>
                           <p class="mb-0 lead">We believe you should have full control over the money you earn. Our system is built for clarity and immediate access.</p>
                        </div>
                        <ul class="list-unstyled mb-5">
                           <li class="mb-3 d-flex">
                              <i class="bi bi-eye-fill text-primary mt-1 fs-4"></i>
                              <span class="ms-3">
                                 <strong>Track Your Earnings in Real-Time:</strong> See your commissions the moment they come in.
                              </span>
                           </li>
                           <li class="mb-3 d-flex">
                              <i class="bi bi-clock-history text-primary mt-1 fs-4"></i>
                              <span class="ms-3">
                                 <strong>Access Funds Instantly:</strong> Your money is available immediately. No more waiting for payout periods.
                              </span>
                           </li>
                           <li class="mb-3 d-flex">
                              <i class="bi bi-bank2 text-primary mt-1 fs-4"></i>
                              <span class="ms-3">
                                 <strong>Use or Withdraw Anytime:</strong> Transfer your earnings to your bank or use them to pay your own bills directly from the app.
                              </span>
                           </li>
                        </ul> 
                     </div>
                  </div>
                  <div class="col-lg-6 offset-lg-1 col-md-6 col-12" data-cue="slideInRight">
                     <div class="position-relative">
                        <figure>
                           <img src="./assets/images/landings/saas/saas-img-1.jpg" alt="Agent dashboard on phone" class="img-fluid rounded-4" />
                        </figure>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action start-->
         <section class="py-5" id="apply">
            <div class="container" data-cues="zoomIn">
               <div class="row bg-pattern bg-primary-gradient rounded-3 p-7 g-0">
                  <div class="col-xl-8 offset-xl-2 col-lg-10 offset-lg-1 col-md-12 col-12">
                     <div class="position-relative z-1 my-lg-5">
                        <div class="mb-5 text-center">
                           <h3 class="h2 text-white-stable mb-1">Ready to Build Your Business with Aidapay?</h3>
                           <p class="mb-0 text-white-stable">Apply to become an agent today. It's free, takes less than 5 minutes, and is the first step towards building your lifetime income stream.</p>
                        </div>
                        <form class="row g-2 needs-validation d-flex mx-lg-7" novalidate>
                           <div class="col-md-7 col-xl-7 col-12">
                              <label for="notificationEmail" class="visually-hidden"></label>
                              <input type="email" id="notificationEmail" class="form-control" placeholder="Enter your email to get started" aria-label="Enter your email to get started" required />
                              <div class="invalid-feedback text-start">Please enter a valid email.</div>
                           </div>
                           <div class="col-md-5 col-xl-5 col-12">
                              <div class="d-grid">
                                 <button class="btn btn-dark" type="submit">Apply Now for Free</button>
                              </div>
                           </div>
                        </form>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action end-->
      </main>
      @@include('./partials/footer-aidapay.html', {
         "webRoot": "."
      })
      @@include('./partials/btn-scroll-top.html')
      @@include('./partials/scripts.html', {
         "webRoot": "."
      })
      <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="./assets/js/vendors/scrollcue.js"></script>
      <script>
         // JavaScript for updating the earnings table based on slider input
         const userSlider = document.getElementById('userSlider');
         const userCountDisplay = document.getElementById('userCount');
         const transactionSlider = document.getElementById('transactionSlider');
         const transactionCountDisplay = document.getElementById('transactionCount');
         const earningsTableBody = document.getElementById('earningsTableBody');
         
         // Update the displayed user count and table values
         function updateEarnings() {
             const newUsersPerMonth = parseInt(userSlider.value);
             const transactionsPerUser = parseInt(transactionSlider.value);
         
             userCountDisplay.innerText = `${newUsersPerMonth} Users`;
             transactionCountDisplay.innerText = `${transactionsPerUser} Transactions`;
         
             // Calculate and update each row in the table
             for (let i = 0; i < earningsTableBody.rows.length; i++) {
                 const row = earningsTableBody.rows[i];
                 const month = parseInt(row.cells[1].getAttribute('data-month'));
                 
                 const totalUsers = newUsersPerMonth * month;
                 const totalTransactions = totalUsers * transactionsPerUser;
                 const totalIncome = totalTransactions * 5; // ₦5 commission per transaction
         
                 row.cells[1].innerText = totalUsers.toLocaleString();
                 row.cells[2].innerText = totalTransactions.toLocaleString();
                 row.cells[3].innerHTML = `<span class="badge bg-success-subtle text-success-emphasis rounded-pill fs-5">~ ₦${totalIncome.toLocaleString()}</span>`;
             }
         }
         
         // Initial calculation
         updateEarnings();
         
         // Event listeners for slider input
         userSlider.addEventListener('input', updateEarnings);
         transactionSlider.addEventListener('input', updateEarnings);
      </script>
   </body>
</html>