#!/bin/bash

# Build HTML files with dynamic header and footer
# This script processes the source HTML files and generates the final files with included partials

echo "🚀 Building HTML files with dynamic header and footer..."

# Run the HTML build task
npx gulp html

if [ $? -eq 0 ]; then
    echo "✅ HTML build completed successfully!"
    echo ""
    echo "📁 Generated files in dist/:"
    ls -la dist/*.html | awk '{print "   " $9 " (" $5 " bytes)"}'
    echo ""
    echo "🎉 All pages now use dynamic header and footer components!"
    echo ""
    echo "📝 To update header/footer across all pages:"
    echo "   1. Edit src/partials/navbar-boxed.html (for boxed navbar)"
    echo "   2. Edit src/partials/navbar.html (for standard navbar)"
    echo "   3. Edit src/partials/footer-aidapay.html (for footer)"
    echo "   4. Run this script again: ./build-html.sh"
else
    echo "❌ HTML build failed. Please check the error messages above."
    exit 1
fi
