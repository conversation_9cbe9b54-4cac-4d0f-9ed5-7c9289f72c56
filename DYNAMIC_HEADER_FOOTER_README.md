# Dynamic Header and Footer Implementation

## Overview

This project has been successfully refactored to use dynamic, reusable header and footer components across all HTML pages. The implementation uses Gulp with `gulp-file-include` to process source files and generate the final HTML files with included partials.

## 🎯 What Was Accomplished

✅ **Extracted common header HTML** into reusable component files  
✅ **Extracted common footer HTML** into reusable component files  
✅ **Set up dynamic inclusion system** using Gulp build process  
✅ **Ensured automatic updates** across all pages when components change  

## 📁 File Structure

```
src/
├── partials/
│   ├── navbar.html              # Standard navbar with offcanvas
│   ├── navbar-boxed.html        # Boxed navbar style
│   ├── footer-aidapay.html      # Aidapay-specific footer
│   ├── head/
│   │   ├── meta.html           # Meta tags
│   │   └── head-links.html     # CSS and favicon links
│   ├── scripts.html            # JavaScript includes
│   └── btn-scroll-top.html     # Scroll to top button
├── home.html                   # Source file for home page
├── about.html                  # Source file for about page
├── contact.html                # Source file for contact page
├── service.html                # Source file for services page
├── scan-pay.html               # Source file for scan & pay page
├── signin.html                 # Source file for sign in page
├── signup.html                 # Source file for sign up page
├── pricing.html                # Source file for pricing page
└── index.html                  # Source file for landing page

dist/                           # Generated files (DO NOT EDIT DIRECTLY)
├── home.html                   # Generated home page
├── about.html                  # Generated about page
└── ... (other generated files)
```

## 🔧 How It Works

### 1. Source Files
- All main pages are now stored in `src/` directory
- Each source file uses `@@include()` syntax to include partials
- Example: `@@include('./partials/navbar-boxed.html', {"webRoot": "."})`

### 2. Partial Components
- **navbar.html**: Standard navbar with offcanvas menu (used by index.html, about.html)
- **navbar-boxed.html**: Boxed navbar style (used by home.html, contact.html, service.html, etc.)
- **footer-aidapay.html**: Aidapay-specific footer with contact info and social links
- **No header/footer**: Authentication pages (signin.html, signup.html) don't include header/footer

### 3. Build Process
- Gulp processes source files and includes partials
- Generated files are output to `dist/` directory
- CSS and JS are also processed and optimized

## 🚀 Usage

### Building HTML Files
```bash
# Option 1: Use the custom build script
./build-html.sh

# Option 2: Use Gulp directly
npx gulp html

# Option 3: Full build (includes CSS, JS, images)
npm run build
```

### Updating Header/Footer
1. **Edit the appropriate partial file:**
   - `src/partials/navbar-boxed.html` - for boxed navbar
   - `src/partials/navbar.html` - for standard navbar  
   - `src/partials/footer-aidapay.html` - for footer

2. **Run the build process:**
   ```bash
   ./build-html.sh
   ```

3. **Changes automatically apply to all pages** that use that partial

### Adding New Pages
1. **Create source file** in `src/` directory
2. **Include appropriate partials:**
   ```html
   <!doctype html>
   <html lang="en">
      <head>
         @@include('./partials/head/meta.html')
         @@include('./partials/head/head-links.html', {"webRoot": "."})
         <title>Page Title</title>
      </head>
      <body>
         @@include('./partials/navbar-boxed.html', {"webRoot": "."})
         
         <main>
            <!-- Your page content here -->
         </main>
         
         @@include('./partials/footer-aidapay.html', {"webRoot": "."})
         @@include('./partials/btn-scroll-top.html')
         @@include('./partials/scripts.html', {"webRoot": "."})
      </body>
   </html>
   ```
3. **Run build process** to generate the final file

## 📋 Page Types and Their Components

| Page Type | Navbar | Footer | Examples |
|-----------|--------|--------|----------|
| **Main Pages** | navbar-boxed.html | footer-aidapay.html | home.html, contact.html, service.html |
| **Landing Pages** | navbar.html | footer.html | index.html, about.html |
| **Auth Pages** | None | None | signin.html, signup.html |

## 🎨 Customization

### Navbar Customization
- **Logo**: Update the `<img src="">` in navbar partials
- **Menu Items**: Modify the `<ul class="navbar-nav">` section
- **Styling**: Edit classes or add custom CSS

### Footer Customization
- **Contact Info**: Update email, phone, address in footer-aidapay.html
- **Social Links**: Modify the social media links section
- **Copyright**: Update the copyright text and year

## 🔍 Benefits

1. **Single Source of Truth**: Update header/footer in one place, applies everywhere
2. **Consistency**: All pages use the same header/footer structure
3. **Maintainability**: Easy to update branding, navigation, or contact information
4. **Efficiency**: No need to manually update multiple files
5. **Version Control**: Track changes to header/footer components separately

## 🛠️ Development Workflow

1. **Make changes** to source files in `src/`
2. **Test locally** by running `./build-html.sh`
3. **Review generated files** in `dist/`
4. **Commit changes** to version control
5. **Deploy** the `dist/` files to production

## ⚠️ Important Notes

- **Never edit files in `dist/` directly** - they will be overwritten
- **Always edit source files** in `src/` directory
- **Run build process** after making changes
- **Test thoroughly** after updates to ensure all pages work correctly

## 🆘 Troubleshooting

### Build Fails
- Check for syntax errors in source files
- Ensure all partial files exist
- Verify Gulp is installed: `npm install`

### Missing Styles/Scripts
- Check that CSS/JS files are properly linked in head-links.html and scripts.html
- Run full build: `npm run build`

### Partial Not Found
- Verify the path in `@@include()` statement
- Check that the partial file exists in `src/partials/`

## 📞 Support

For questions or issues with the dynamic header/footer system:
1. Check this documentation
2. Review the source files for examples
3. Test with the build script: `./build-html.sh`
